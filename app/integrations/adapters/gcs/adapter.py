from typing import <PERSON>ary<PERSON>

from app.common.helpers.logger import get_logger
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.file_adapter import BaseFileAdapter
from app.integrations.types import IntegrationSource

logger = get_logger()


class GCSAdapter(BaseFileAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self._client = self._create_client(credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.GCS

    def _create_client(self, credentials: ICredentials):
        pass

    async def upload_file(
        self, bucket_name: str, file_obj: BinaryIO, file_name: str
    ) -> None:
        pass

    async def download_file(self, bucket_name: str, file_name: str) -> bytes:  # noqa: ARG002
        return b""

    async def delete_file(self, bucket_name: str, file_name: str) -> None:
        pass

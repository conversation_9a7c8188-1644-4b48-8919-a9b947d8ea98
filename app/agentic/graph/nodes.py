from datetime import UTC, datetime

from langchain_core.messages import BaseMessage

from app.agentic.context.account import get_account_details
from app.agentic.graph.prompts import AgentPrompts
from app.agentic.graph.state import ConversationState
from app.workspace.integrations.user_integrations import UserIntegrations


async def fetch_account_node(
    state: ConversationState,
    user_integrations: UserIntegrations,
) -> ConversationState:
    account_details = await get_account_details(
        state["user_id"],
        state["crm_account_id"],
        user_integrations,
    )

    state["account_info"] = account_details
    state["last_refetch_at"] = datetime.now(UTC)
    return state


async def context_injector_node(
    state: ConversationState, agent_prompts: AgentPrompts
) -> ConversationState:
    account_info = state.get("account_info")
    if not account_info:
        return state

    if any(msg.name == "account_context" for msg in state["messages"]):
        return state

    context_message = agent_prompts.format_account_context_message(account_info)
    new_messages: list[BaseMessage] = [context_message, *state["messages"]]

    state["messages"] = new_messages
    return state
